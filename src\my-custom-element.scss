@use "node_modules/@bbva-web-components/bbva-foundations-sass/main" as *;

:host {
  display: block;
  box-sizing: border-box;
  font-family: '<PERSON> Sans', <PERSON><PERSON>, sans-serif;
}

:host([hidden]), [hidden] {
  display: none !important;
}

*, *::before, *::after {
  box-sizing: inherit;
}

/* Estilos para la tabla */
table {
  width: 100%;
  border-collapse: collapse;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

thead {
  background-color: #f5f5f5;
}

th, td {
  padding: 16px 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

/* Alineación específica para columnas */
th:nth-child(4), td:nth-child(4) { /* Columna IMPORTE */
  text-align: right;
}

th:nth-child(5), td:nth-child(5) { /* Columna de menú (3 puntos) */
  text-align: left;
}

th {
  font-weight: normal;
  color: #333333;
  font-size: 16px;
}

tbody tr {
  transition: background-color 0.2s ease;
}

tbody tr:hover {
  background-color: #f9f9f9;
}

/* Estilos para el menú contextual */
.action-menu {
  position: relative;
  display: inline-block;
}

.menu-trigger {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  font-size: 18px;
  font-weight: normal;
  color: #666666;
  user-select: none;
}

.menu-trigger:hover {
  background-color: #f0f0f0;
  color: #333333;
}

.menu-content {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 120px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
}

.action-menu.open .menu-content {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.menu-content bbva-web-link {
  display: block;
  padding: 12px 16px;
  text-decoration: none;
  color: #333333;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.menu-content bbva-web-link:last-child {
  border-bottom: none;
}

.menu-content bbva-web-link:hover {
  background-color: #f9f9f9;
}

/* Estilos responsivos */
@media (max-width: 768px) {
  table {
    font-size: 14px;
  }

  th, td {
    padding: 12px 8px;
  }

  .menu-content {
    right: -20px;
  }
}

/* Estilos para el footer de la tabla */
tfoot td {
  border-bottom: none;
  padding: 16px;
  text-align: center;
}

.show-more-button {
  color: #004481;
  cursor: pointer;
  text-decoration: none;
  font-size: 14px;
}

.show-more-button:hover {
  text-decoration: underline;
}

/* Mejoras visuales adicionales */
bbva-web-amount {
  font-weight: normal;
}

bbva-web-table-header-text {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

bbva-web-table-body-text {
  font-size: 14px;
  line-height: 1.4;
}
