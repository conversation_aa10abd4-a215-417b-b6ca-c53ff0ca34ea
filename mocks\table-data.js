export const tableData = [
  {
    cuentaDestino: "00110300105082222067",
    destinatario: "<PERSON>",
    conceptoPago: "Pago de carpintería...",
    importe: "2500.00",
    currencyCode: "PEN"
  },
  {
    cuentaDestino: "00110300105082222145",
    destinatario: "<PERSON><PERSON>",
    conceptoPago: "Pago de plomería", 
    importe: "1850.50",
    currencyCode: "USD"
  },
  {
    cuentaDestino: "00110300105082222223",
    destinatario: "<PERSON>",
    conceptoPago: "Servicios de consultoría",
    importe: "3200.00",
    currencyCode: "EUR"
  },
  {
    cuentaDestino: "00110300105082222301",
    destinatario: "<PERSON>",
    conceptoPago: "Pago de servicios médicos",
    importe: "750.25",
    currencyCode: "PEN"
  },
  {
    cuentaDestino: "00110300105082222489",
    destinatario: "<PERSON>",
    conceptoPago: "Compra de equipos de oficina",
    importe: "4500.00",
    currencyCode: "PEN"
  },
  {
    cuentaDestino: "00110300105082222567",
    destinatario: "Maria Elena <PERSON>",
    conceptoPago: "Pago de alquiler mensual",
    importe: "1200.00",
    currencyCode: "USD"
  },
  {
    cuentaDestino: "00110300105082222645",
    destinatario: "Roberto Silva Morales",
    conceptoPago: "Servicios de marketing digital",
    importe: "2800.75",
    currencyCode: "PEN"
  },
  {
    cuentaDestino: "00110300105082222723",
    destinatario: "Patricia Gonzalez Lima",
    conceptoPago: "Pago de honorarios profesionales",
    importe: "1500.00",
    currencyCode: "EUR"
  },
  {
    cuentaDestino: "00110300105082222801",
    destinatario: "Diego Fernando Ruiz",
    conceptoPago: "Compra de materiales de construcción",
    importe: "5200.30",
    currencyCode: "PEN"
  },
  {
    cuentaDestino: "00110300105082222989",
    destinatario: "Claudia Beatriz Torres",
    conceptoPago: "Servicios de limpieza",
    importe: "680.00",
    currencyCode: "PEN"
  },
  {
    cuentaDestino: "00110300105082223067",
    destinatario: "Fernando Jose Herrera",
    conceptoPago: "Pago de seguros",
    importe: "950.50",
    currencyCode: "USD"
  },
  {
    cuentaDestino: "00110300105082223145",
    destinatario: "Gabriela Ines Vargas",
    conceptoPago: "Servicios de contabilidad",
    importe: "1800.00",
    currencyCode: "PEN"
  },
  {
    cuentaDestino: "00110300105082223223",
    destinatario: "Alejandro Miguel Santos",
    conceptoPago: "Compra de software empresarial",
    importe: "3500.00",
    currencyCode: "EUR"
  },
  {
    cuentaDestino: "00110300105082223301",
    destinatario: "Valeria Nicole Campos",
    conceptoPago: "Pago de servicios legales",
    importe: "2200.75",
    currencyCode: "PEN"
  },
  {
    cuentaDestino: "00110300105082223489",
    destinatario: "Ricardo Andres Mendez",
    conceptoPago: "Servicios de transporte",
    importe: "420.00",
    currencyCode: "PEN"
  },
  {
    cuentaDestino: "00110300105082223567",
    destinatario: "Stephanie Carol Jimenez",
    conceptoPago: "Pago de publicidad online",
    importe: "1650.25",
    currencyCode: "USD"
  },
  {
    cuentaDestino: "00110300105082223645",
    destinatario: "Andres Felipe Rojas",
    conceptoPago: "Servicios de mantenimiento",
    importe: "890.50",
    currencyCode: "PEN"
  },
  {
    cuentaDestino: "00110300105082223723",
    destinatario: "Natalia Esperanza Cruz",
    conceptoPago: "Compra de mobiliario",
    importe: "3800.00",
    currencyCode: "PEN"
  },
  {
    cuentaDestino: "00110300105082223801",
    destinatario: "Sebastian Omar Delgado",
    conceptoPago: "Servicios de desarrollo web",
    importe: "2750.00",
    currencyCode: "EUR"
  },
  {
    cuentaDestino: "00110300105082223989",
    destinatario: "Isabella Maria Flores",
    conceptoPago: "Pago de capacitación empresarial",
    importe: "1320.80",
    currencyCode: "PEN"
  }
];
